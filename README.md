# XWiki Watermark Extension

A professional watermark extension for XWiki 17.4.3 that provides dynamic text watermarks with placeholder support, anti-copy protection, and mobile compatibility.

## Features

- **Dynamic Watermark Rendering**: High-quality Canvas-based watermark generation
- **Placeholder Support**: Supports `${user}` and `${timestamp}` placeholders with automatic replacement
- **Flexible Configuration**: Configurable watermark text, style, position, transparency, and other parameters
- **Anti-Copy Protection**: Optional content protection features to prevent selection and copying
- **Export Permission Control**: Restricts export functionality to authorized users only
- **Mobile Adaptation**: Responsive design with mobile device support
- **Multi-language Support**: Automatic switching between Chinese/English
- **XWiki Standard Integration**: Integrated into XWiki's standard management interface

## Installation

1. Build the extension:
   ```bash
   mvn clean package
   ```

2. Upload the generated XAR file (`target/xwiki-watermark-extension-1.0-SNAPSHOT.xar`) to your XWiki instance through the Extension Manager.

3. The extension will be automatically installed and ready to use.

## Configuration

1. Access `http://localhost:8080/bin/admin/XWiki/XWikiPreferences`
2. In the left navigation, find **"Watermark"** under the **"Look & Feel"** category
3. Check the **"Enable Watermark"** option
4. Adjust configuration parameters as needed
5. Click **"Save Configuration"** button

### Configuration Parameters

| Parameter | Type | Description | Default Value |
|-----------|------|-------------|---------------|
| enabled | boolean | Enable or disable watermark | false |
| textTemplate | string | Watermark text template | "${user} - ${timestamp}" |
| xSpacing | integer | Horizontal spacing (pixels) | 200 |
| ySpacing | integer | Vertical spacing (pixels) | 100 |
| angle | integer | Rotation angle (degrees) | -30 |
| opacity | float | Transparency (0.0-1.0) | 0.3 |
| fontSize | integer | Font size (pixels) | 14 |
| antiCopy | boolean | Enable anti-copy protection | false |
| applyToMobile | boolean | Apply to mobile devices | true |

## Export Permission Control

This extension includes advanced export permission control functionality that restricts export capabilities to authorized users only.

### How It Works

- **User Group Based**: Only users in the `XWikiCopyGroup` are allowed to export content
- **Dual Protection**: Implements both frontend UI control and backend permission validation
- **Comprehensive Coverage**: Blocks all export formats (PDF, HTML, XAR, ODT, RTF)
- **Multiple Access Points**: Controls export buttons, dropdown menus, direct URL access, and API calls

### Frontend Protection

- Automatically hides export-related UI elements for non-authorized users
- Intercepts export-related clicks and displays permission denied messages
- Uses comprehensive CSS selectors to cover all possible export entry points
- Includes periodic checks for dynamically loaded content

### Backend Protection

- Validates user permissions on all export requests
- Returns HTTP 403 Forbidden status for unauthorized access attempts
- Provides user-friendly error pages with clear permission explanations
- Blocks direct URL access to export endpoints

### User Experience

- **Authorized Users (XWikiCopyGroup)**: Full export functionality with no restrictions
- **Non-Authorized Users**: Export options are hidden, with clear permission messages when needed
- **Bilingual Support**: Error messages and notifications in both Chinese and English

### Security Features

- **URL Interception**: Blocks direct access to export URLs
- **AJAX Protection**: Intercepts XMLHttpRequest and fetch API calls
- **Form Submission Control**: Prevents export form submissions
- **Navigation Blocking**: Prevents programmatic navigation to export URLs

## Architecture

This extension uses a simplified architecture with the following core files:

### Core Components
- **WatermarkConfigClass.xml**: Configuration class definition
- **WatermarkAdmin.xml**: Management interface (integrated into Look & Feel)
- **WatermarkSkinExtension.xml**: JavaScript implementation with export permission control
- **WatermarkStyleExtension.xml**: CSS styles
- **Translation files**: Multi-language support

### Export Permission Control Components
- **ExportPermissionCheck.xml**: Backend permission validation page
- **ExportInterceptor.xml**: Advanced URL and API interception component

### Architecture Benefits
- **Minimal Dependencies**: Uses only XWiki standard APIs
- **Modular Design**: Clear separation between watermark and permission control features
- **Performance Optimized**: Lightweight implementation with efficient permission checking

## Technical Requirements

- XWiki 17.x
- Java 17.x  
- Maven 3.x

## Development

This extension follows XWiki's recommended practices:
- Uses XAR packaging format for easy installation without XWiki restarts
- Follows "function first, code simplicity" principle
- Minimal architecture with clear separation of concerns

## License

This software is licensed under the GNU Lesser General Public License (LGPL) version 2.1 or later.

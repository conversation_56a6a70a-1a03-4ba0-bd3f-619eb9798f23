<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.ExportInterceptor" locale="">
  <web>WatermarkExtension</web>
  <name>ExportInterceptor</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Export Interceptor</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>true</hidden>
  <object>
    <name>WatermarkExtension.ExportInterceptor</name>
    <number>0</number>
    <className>XWiki.XWikiRights</className>
    <guid>export-interceptor-rights-allow</guid>
    <class>
      <name>XWiki.XWikiRights</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <allow>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
        <customDisplay/>
        <defaultValue>1</defaultValue>
        <displayFormType>select</displayFormType>
        <displayType>allow</displayType>
        <name>allow</name>
        <number>4</number>
        <prettyName>Allow/Deny</prettyName>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </allow>
      <groups>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.GroupsClass</classType>
        <customDisplay/>
        <defaultValue/>
        <displayFormType>input</displayFormType>
        <displayType>groups</displayType>
        <multiSelect>1</multiSelect>
        <name>groups</name>
        <number>1</number>
        <picker>1</picker>
        <prettyName>Groups</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>5</size>
        <unmodifiable>0</unmodifiable>
        <usesList>1</usesList>
        <validationMessage/>
        <validationRegExp/>
      </groups>
      <levels>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.LevelsClass</classType>
        <customDisplay/>
        <defaultValue/>
        <displayType>levels</displayType>
        <multiSelect>1</multiSelect>
        <name>levels</name>
        <number>2</number>
        <prettyName>Levels</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>3</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </levels>
      <users>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.UsersClass</classType>
        <customDisplay/>
        <defaultValue/>
        <displayFormType>input</displayFormType>
        <displayType>users</displayType>
        <multiSelect>1</multiSelect>
        <name>users</name>
        <number>3</number>
        <picker>1</picker>
        <prettyName>Users</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>5</size>
        <unmodifiable>0</unmodifiable>
        <usesList>1</usesList>
        <validationMessage/>
        <validationRegExp/>
      </users>
    </class>
    <property>
      <allow>1</allow>
    </property>
    <property>
      <groups>XWiki.XWikiAllGroup</groups>
    </property>
    <property>
      <levels>view</levels>
    </property>
    <property>
      <users/>
    </property>
  </object>
  <content><![CDATA[
{{velocity}}
## Export Interceptor - JavaScript-based URL interception
## This creates a client-side interceptor for export URLs

#if("$!xcontext.user" == "")
  #set($wmUserRef = "XWikiGuest")
#else
  #set($wmUserRef = "$xcontext.user")
#end

#set($isInCopyGroup = false)
#if($wmUserRef != "XWikiGuest")
  #set($isInCopyGroup = $xwiki.getUser($wmUserRef).isUserInGroup('XWiki.XWikiCopyGroup'))
#end

#if(!$isInCopyGroup)
{{/velocity}}

<script type="text/javascript">
/* Export URL Interceptor for Non-Privileged Users */
(function() {
  'use strict';
  
  // Override XMLHttpRequest to intercept AJAX export requests
  var originalXHROpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
    if (url && (url.includes('/bin/export') || url.includes('format='))) {
      console.warn('Export request blocked:', url);
      alert('您没有导出权限，只有XWikiCopyGroup组的用户才能导出内容。\nYou do not have export permission. Only users in the XWikiCopyGroup can export content.');
      return;
    }
    return originalXHROpen.call(this, method, url, async, user, password);
  };
  
  // Override fetch API to intercept modern AJAX requests
  if (window.fetch) {
    var originalFetch = window.fetch;
    window.fetch = function(input, init) {
      var url = typeof input === 'string' ? input : input.url;
      if (url && (url.includes('/bin/export') || url.includes('format='))) {
        console.warn('Export fetch request blocked:', url);
        alert('您没有导出权限，只有XWikiCopyGroup组的用户才能导出内容。\nYou do not have export permission. Only users in the XWikiCopyGroup can export content.');
        return Promise.reject(new Error('Export permission denied'));
      }
      return originalFetch.call(this, input, init);
    };
  }
  
  // Intercept direct URL navigation
  var originalPushState = history.pushState;
  var originalReplaceState = history.replaceState;
  
  history.pushState = function(state, title, url) {
    if (url && (url.includes('/bin/export') || url.includes('format='))) {
      console.warn('Export navigation blocked:', url);
      alert('您没有导出权限，只有XWikiCopyGroup组的用户才能导出内容。\nYou do not have export permission. Only users in the XWikiCopyGroup can export content.');
      return;
    }
    return originalPushState.call(this, state, title, url);
  };
  
  history.replaceState = function(state, title, url) {
    if (url && (url.includes('/bin/export') || url.includes('format='))) {
      console.warn('Export navigation blocked:', url);
      alert('您没有导出权限，只有XWikiCopyGroup组的用户才能导出内容。\nYou do not have export permission. Only users in the XWikiCopyGroup can export content.');
      return;
    }
    return originalReplaceState.call(this, state, title, url);
  };
  
  // Monitor for direct URL access attempts
  window.addEventListener('beforeunload', function(e) {
    var currentUrl = window.location.href;
    if (currentUrl.includes('/bin/export') || currentUrl.includes('format=')) {
      e.preventDefault();
      e.returnValue = '您没有导出权限 / You do not have export permission';
      return e.returnValue;
    }
  });
  
  // Check current URL on page load
  if (window.location.href.includes('/bin/export') || window.location.href.includes('format=')) {
    // Redirect to permission denied page
    window.location.href = '/bin/view/WatermarkExtension/ExportPermissionCheck';
  }

  console.log('Export URL interceptor activated for non-privileged user');
})();
</script>

{{velocity}}
#else
{{/velocity}}

<script type="text/javascript">
/* User has export permission - no interception needed */
console.log('User has export permission - export interceptor not activated');
</script>

{{velocity}}
#end
{{/velocity}}
]]></content>
</xwikidoc>

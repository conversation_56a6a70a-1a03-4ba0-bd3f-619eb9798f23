<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.ExportPermissionCheck" locale="">
  <web>WatermarkExtension</web>
  <name>ExportPermissionCheck</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Export Permission Check</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>true</hidden>
  <object>
    <name>WatermarkExtension.ExportPermissionCheck</name>
    <number>0</number>
    <className>XWiki.XWikiRights</className>
    <guid>export-permission-check-rights-allow</guid>
    <class>
      <name>XWiki.XWikiRights</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <allow>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
        <customDisplay/>
        <defaultValue>1</defaultValue>
        <displayFormType>select</displayFormType>
        <displayType>allow</displayType>
        <name>allow</name>
        <number>4</number>
        <prettyName>Allow/Deny</prettyName>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </allow>
      <groups>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.GroupsClass</classType>
        <customDisplay/>
        <defaultValue/>
        <displayFormType>input</displayFormType>
        <displayType>groups</displayType>
        <multiSelect>1</multiSelect>
        <name>groups</name>
        <number>1</number>
        <picker>1</picker>
        <prettyName>Groups</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>5</size>
        <unmodifiable>0</unmodifiable>
        <usesList>1</usesList>
        <validationMessage/>
        <validationRegExp/>
      </groups>
      <levels>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.LevelsClass</classType>
        <customDisplay/>
        <defaultValue/>
        <displayType>levels</displayType>
        <multiSelect>1</multiSelect>
        <name>levels</name>
        <number>2</number>
        <prettyName>Levels</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>3</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </levels>
      <users>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.UsersClass</classType>
        <customDisplay/>
        <defaultValue/>
        <displayFormType>input</displayFormType>
        <displayType>users</displayType>
        <multiSelect>1</multiSelect>
        <name>users</name>
        <number>3</number>
        <picker>1</picker>
        <prettyName>Users</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>5</size>
        <unmodifiable>0</unmodifiable>
        <usesList>1</usesList>
        <validationMessage/>
        <validationRegExp/>
      </users>
    </class>
    <property>
      <allow>1</allow>
    </property>
    <property>
      <groups>XWiki.XWikiAllGroup</groups>
    </property>
    <property>
      <levels>view</levels>
    </property>
    <property>
      <users/>
    </property>
  </object>
  <content><![CDATA[
{{velocity}}
## Export Permission Check - Backend Validation
## This page provides backend validation for export permissions
## Only users in XWikiCopyGroup are allowed to export content

## Check if this is an export request by examining the request parameters
#set($isExportRequest = false)
#set($requestURI = $request.requestURI)
#set($queryString = $request.queryString)

## Check if this is an export-related request
#if($requestURI.contains('/bin/export') ||
    ($queryString && ($queryString.contains('format=pdf') ||
                     $queryString.contains('format=html') ||
                     $queryString.contains('format=xar') ||
                     $queryString.contains('format=odt') ||
                     $queryString.contains('format=rtf'))))
  #set($isExportRequest = true)
#end

## If this is not an export request, show normal page content
#if(!$isExportRequest)
  = Export Permission Check =

  This page is used for backend export permission validation.

  {{info}}
  This is a system page used by the XWiki Watermark Extension to validate export permissions.
  Only users in the XWikiCopyGroup are allowed to export content.
  {{/info}}

  #stop
#end

## Get current user information for export requests
#if("$!xcontext.user" == "")
  #set($wmUserRef = "XWikiGuest")
#else
  #set($wmUserRef = "$xcontext.user")
#end

## Extract user display name
#set($wmUserPretty = $xwiki.getUserName($wmUserRef, false))

## Extract pure username (remove XWiki. prefix)
#if($wmUserRef == "XWikiGuest")
  #set($wmUserId = "Guest")
#else
  #set($wmUserId = $wmUserRef.substring($wmUserRef.indexOf('.') + 1))
#end

## Check if user is in XWikiCopyGroup
#set($isInCopyGroup = false)
#if($wmUserRef != "XWikiGuest")
  #set($isInCopyGroup = $xwiki.getUser($wmUserRef).isUserInGroup('XWiki.XWikiCopyGroup'))
#end

## If user doesn't have export permission, return 403 error
#if(!$isInCopyGroup)
  ## Set HTTP 403 status code
  $response.setStatus(403)
  $response.setContentType("text/html; charset=UTF-8")
  
  ## Return permission denied page
  <!DOCTYPE html>
  <html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出权限不足 - Export Permission Denied</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 40px 20px;
        color: #333;
        line-height: 1.6;
      }
      .container {
        max-width: 600px;
        margin: 0 auto;
        background: white;
        padding: 40px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
      }
      .error-icon {
        font-size: 64px;
        color: #dc3545;
        margin-bottom: 20px;
      }
      h1 {
        color: #dc3545;
        margin-bottom: 20px;
        font-size: 28px;
      }
      .message {
        margin-bottom: 30px;
        font-size: 16px;
        color: #666;
      }
      .user-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 30px;
        font-size: 14px;
        color: #555;
      }
      .actions {
        margin-top: 30px;
      }
      .btn {
        display: inline-block;
        padding: 12px 24px;
        margin: 0 10px;
        text-decoration: none;
        border-radius: 4px;
        font-weight: 500;
        transition: background-color 0.2s;
      }
      .btn-primary {
        background-color: #007bff;
        color: white;
      }
      .btn-primary:hover {
        background-color: #0056b3;
        color: white;
      }
      .btn-secondary {
        background-color: #6c757d;
        color: white;
      }
      .btn-secondary:hover {
        background-color: #545b62;
        color: white;
      }
      .footer {
        margin-top: 40px;
        font-size: 12px;
        color: #999;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="error-icon">🚫</div>
      <h1>导出权限不足 / Export Permission Denied</h1>
      
      <div class="message">
        <p><strong>中文说明：</strong></p>
        <p>您没有导出权限。只有XWikiCopyGroup组的用户才能导出内容。</p>
        <p>如需导出权限，请联系系统管理员。</p>
        
        <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
        
        <p><strong>English Description:</strong></p>
        <p>You do not have export permission. Only users in the XWikiCopyGroup can export content.</p>
        <p>Please contact the system administrator if you need export access.</p>
      </div>
      
      <div class="user-info">
        <strong>当前用户信息 / Current User Information:</strong><br>
        用户名 / Username: $escapetool.html($wmUserId)<br>
        显示名 / Display Name: $escapetool.html($wmUserPretty)<br>
        用户组权限 / Group Permission: 无导出权限 / No Export Permission
      </div>
      
      <div class="actions">
        <a href="javascript:history.back()" class="btn btn-secondary">返回上页 / Go Back</a>
        <a href="/bin/view/Main/" class="btn btn-primary">返回首页 / Home</a>
      </div>
      
      <div class="footer">
        <p>XWiki Watermark Extension - Export Permission Control</p>
        <p>如有疑问请联系管理员 / For questions, please contact the administrator</p>
      </div>
    </div>
  </body>
  </html>
  
  ## Stop processing here for unauthorized users
  #stop
#end

## If user has permission, allow the export to proceed
## This page can be used as a permission check endpoint
## Return success status for authorized users
$response.setStatus(200)
$response.setContentType("application/json; charset=UTF-8")

{
  "status": "success",
  "message": "Export permission granted",
  "user": "$escapetool.javascript($wmUserId)",
  "hasExportPermission": true,
  "timestamp": "$datetool.get('yyyy-MM-dd HH:mm:ss')"
}
{{/velocity}}
]]></content>
</xwikidoc>
